# ========================================
# GITIGNORE COMPLET POUR KAYDAN ANALYTICS HUB
# ========================================

# ========================================
# ENVIRONNEMENTS VIRTUELS
# ========================================
venv/
env/
ENV/
.venv/
.env/
backend/venv/
backend/env/
frontend/node_modules/

# ========================================
# FICHIERS DE CONFIGURATION SENSIBLES
# ========================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.key
*.pem
*.p12
secrets.json
config/secrets/

# ========================================
# BASES DE DONNÉES
# ========================================
*.db
*.sqlite
*.sqlite3
db.sqlite3
database.db
*.db-journal

# ========================================
# FICHIERS DE TEST ET DOCUMENTATION
# ========================================
# Fichiers README
README.md
README.txt
readme.md
readme.txt
LISEZMOI.md
LISEZMOI.txt

# Fichiers de test
test_*.py
*_test.py
tests/
test/
*test*.py
backend/test_*.py
backend/*test*.py

# Documentation générée
docs/
documentation/
*.md
!requirements.md
GUIDE_*.md
RAPPORT_*.md
RESUME_*.md

# ========================================
# DONNÉES EXTERNES
# ========================================
data/
datasets/
csv_files/
excel_files/
*.csv
*.xlsx
*.xls
*.json
!package.json
!package-lock.json

# ========================================
# LOGS ET FICHIERS TEMPORAIRES
# ========================================
# Logs
*.log
logs/
log/
backend/*.log
backend/logs/
analytics.log
debug.log
error.log

# Fichiers temporaires
*.tmp
*.temp
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# ========================================
# PYTHON
# ========================================
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
local_settings.py
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# ========================================
# NODE.JS / FRONTEND
# ========================================
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript v1 declaration files
typings/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# ========================================
# SYSTÈMES D'EXPLOITATION
# ========================================
# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ========================================
# ÉDITEURS ET IDE
# ========================================
# VSCode
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ========================================
# AUTRES FICHIERS À IGNORER
# ========================================
# Fichiers de sauvegarde
*.bak
*.backup
*.old
*.orig

# Fichiers compressés
*.zip
*.tar.gz
*.rar
*.7z

# Fichiers média (si pas nécessaires)
*.mp4
*.avi
*.mov
*.wmv
*.mp3
*.wav
*.flac

# Certificats et clés
*.crt
*.csr
*.key
*.pem
*.p12
*.pfx

# Fichiers de cache
.cache/
cache/
*.cache

# Fichiers de configuration locaux
local_config.py
local_settings.py
config.local.json

# ========================================
# EXCEPTIONS (FICHIERS À GARDER)
# ========================================
# Garder certains fichiers importants
!.gitkeep
!requirements.txt
!package.json
!docker-compose.yml
!Dockerfile
!.github/
!.gitlab-ci.yml
