from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    # ViewSets utilisateur
    UserProfileViewSet,

    # ViewSets données externes existantes
    KreDQEData,
    GStockApprovisionnementViewSet,
    EcoleTalentsViewSet,
    KreGLocativeData,
    KreESyndicData,
    # Nouveaux ViewSets pour les données externes
    DonneesProduitInterieurBrutViewSet,
    DonneesInflationViewSet,
    DonnesTauxPretImmobilierViewSet,
    DonneesPrixMetreCarreViewSet,
    DonneesMateriauxConstructionViewSet,
    DonneesProjectionDemographiqueViewSet,
    DonneesMigrationInterneViewSet,
)

# ========================================
# CONFIGURATION DU ROUTER
# ========================================

router = DefaultRouter()

# ViewSets utilisateur
router.register(r"profiles", UserProfileViewSet, basename="profile")

# ViewSets données externes existantes
router.register(r"dqe", KreDQEData, basename="dqe-data")
router.register(
    r"gstock-approvisionnement",
    GStockApprovisionnementViewSet,
    basename="gstock-approvisionnement",
)
router.register(r"ecole-talents", EcoleTalentsViewSet, basename="ecole-talents")
router.register(r"glocative", KreGLocativeData, basename="glocative-data")
router.register(r"esyndic", KreESyndicData, basename="esyndic-data")

# Nouveaux ViewSets pour les données externes
# Données macroéconomiques
router.register(
    r"donnees-macroeconomiques_DonneesExternes",
    DonneesProduitInterieurBrutViewSet,
    basename="donnees-macroeconomiques"
)
router.register(
    r"donnees-inflation_DonneesExternes",
    DonneesInflationViewSet,
    basename="donnees-inflation"
)
router.register(
    r"taux-pret-immobilier_DonneesExternes",
    DonnesTauxPretImmobilierViewSet,
    basename="taux-pret-immobilier"
)

# Données du marché immobilier
router.register(
    r"prix-metre-carre_DonneesExternes",
    DonneesPrixMetreCarreViewSet,
    basename="prix-metre-carre"
)
router.register(
    r"materiaux-construction_DonneesExternes",
    DonneesMateriauxConstructionViewSet,
    basename="materiaux-construction"
)

# Données démographiques et sociales
router.register(
    r"projections-demographiques_DonneesExternes",
    DonneesProjectionDemographiqueViewSet,
    basename="projections-demographiques"
)
router.register(
    r"migrations-internes_DonneesExternes",
    DonneesMigrationInterneViewSet,
    basename="migrations-internes"
)

urlpatterns = [
    path("", include(router.urls)),
]