{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import os\n", "from datetime import datetime\n", "import plotly.express as px\n", "import plotly.graph_objects as go"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Données JSON chargées depuis gstockapprovisionnement_data.json\n"]}], "source": ["# 1. Chargement des données JSON\n", "json_file = 'gstockapprovisionnement_data.json'\n", "\n", "with open(json_file, 'r', encoding='utf-8') as f:\n", "    json_data = json.load(f)\n", "\n", "print(f\"Données JSON chargées depuis {json_file}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DataFrame créé: (1, 1)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>stocks</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>[{'programme': {'id': 7, 'libelle': 'K2', 'nom...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                              stocks\n", "0  [{'programme': {'id': 7, 'libelle': 'K2', 'nom..."]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# 2. Conversion JSON vers DataFrame\n", "if isinstance(json_data, list):\n", "    df = pd.DataFrame(json_data)\n", "else:\n", "    df = pd.DataFrame([json_data])\n", "\n", "print(f\"DataFrame créé: {df.shape}\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CSV sauvegardé: ../../exports/gstockapprovisionnement_20250714_121000.csv\n", "Nombre de lignes: 1\n"]}], "source": ["# 3. <PERSON><PERSON><PERSON><PERSON> en CSV\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "csv_filename = f'gstockapprovisionnement_{timestamp}.csv'\n", "\n", "os.makedirs('../../exports', exist_ok=True)\n", "csv_filepath = f'../../exports/{csv_filename}'\n", "\n", "df.to_csv(csv_filepath, index=False, encoding='utf-8')\n", "print(f\"CSV sauvegardé: {csv_filepath}\")\n", "print(f\"Nombre de lignes: {len(df)}\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CSV rechargé: (1, 1)\n", "Aperçu des données:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>stocks</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>[{'programme': {'id': 7, 'libelle': 'K2', 'nom...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                              stocks\n", "0  [{'programme': {'id': 7, 'libelle': 'K2', 'nom..."]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# 4. Lecture du CSV sauvegardé\n", "df_loaded = pd.read_csv(csv_filepath)\n", "\n", "print(f\"CSV rechargé: {df_loaded.shape}\")\n", "print(\"Aperçu des données:\")\n", "df_loaded.head()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "Value of 'x' is not the name of a column in 'data_frame'. Expected one of ['stocks'] but received: title", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[8]\u001b[39m\u001b[32m, line 3\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# 5. Visualisations avec Plotly\u001b[39;00m\n\u001b[32m      2\u001b[39m \u001b[38;5;66;03m# Graphique simple\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m3\u001b[39m fig1 = px.bar(df_loaded, x=\u001b[33m'\u001b[39m\u001b[33mtitle\u001b[39m\u001b[33m'\u001b[39m, title=\u001b[33m'\u001b[39m\u001b[33mStock Approvisionnement\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m      4\u001b[39m fig1.show()\n\u001b[32m      6\u001b[39m \u001b[38;5;66;03m# Graphique en secteurs\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/KAH/lib/python3.13/site-packages/plotly/express/_chart_types.py:381\u001b[39m, in \u001b[36mbar\u001b[39m\u001b[34m(data_frame, x, y, color, pattern_shape, facet_row, facet_col, facet_col_wrap, facet_row_spacing, facet_col_spacing, hover_name, hover_data, custom_data, text, base, error_x, error_x_minus, error_y, error_y_minus, animation_frame, animation_group, category_orders, labels, color_discrete_sequence, color_discrete_map, color_continuous_scale, pattern_shape_sequence, pattern_shape_map, range_color, color_continuous_midpoint, opacity, orientation, barmode, log_x, log_y, range_x, range_y, text_auto, title, subtitle, template, width, height)\u001b[39m\n\u001b[32m    332\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mbar\u001b[39m(\n\u001b[32m    333\u001b[39m     data_frame=\u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[32m    334\u001b[39m     x=\u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[32m   (...)\u001b[39m\u001b[32m    375\u001b[39m     height=\u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[32m    376\u001b[39m ) -> go.Figure:\n\u001b[32m    377\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m    378\u001b[39m \u001b[33;03m    In a bar plot, each row of `data_frame` is represented as a rectangular\u001b[39;00m\n\u001b[32m    379\u001b[39m \u001b[33;03m    mark.\u001b[39;00m\n\u001b[32m    380\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m381\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m make_figure(\n\u001b[32m    382\u001b[39m         args=\u001b[38;5;28mlocals\u001b[39m(),\n\u001b[32m    383\u001b[39m         constructor=go.Bar,\n\u001b[32m    384\u001b[39m         trace_patch=\u001b[38;5;28mdict\u001b[39m(textposition=\u001b[33m\"\u001b[39m\u001b[33mauto\u001b[39m\u001b[33m\"\u001b[39m),\n\u001b[32m    385\u001b[39m         layout_patch=\u001b[38;5;28mdict\u001b[39m(barmode=barmode),\n\u001b[32m    386\u001b[39m     )\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/KAH/lib/python3.13/site-packages/plotly/express/_core.py:2479\u001b[39m, in \u001b[36mmake_figure\u001b[39m\u001b[34m(args, constructor, trace_patch, layout_patch)\u001b[39m\n\u001b[32m   2476\u001b[39m layout_patch = layout_patch \u001b[38;5;129;01mor\u001b[39;00m {}\n\u001b[32m   2477\u001b[39m apply_default_cascade(args)\n\u001b[32m-> \u001b[39m\u001b[32m2479\u001b[39m args = build_dataframe(args, constructor)\n\u001b[32m   2480\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m constructor \u001b[38;5;129;01min\u001b[39;00m [go.Treemap, go.Sunburst, go.Icicle] \u001b[38;5;129;01mand\u001b[39;00m args[\u001b[33m\"\u001b[39m\u001b[33mpath\u001b[39m\u001b[33m\"\u001b[39m] \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m   2481\u001b[39m     args = process_dataframe_hierarchy(args)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/KAH/lib/python3.13/site-packages/plotly/express/_core.py:1727\u001b[39m, in \u001b[36mbuild_dataframe\u001b[39m\u001b[34m(args, constructor)\u001b[39m\n\u001b[32m   1724\u001b[39m     args[\u001b[33m\"\u001b[39m\u001b[33mcolor\u001b[39m\u001b[33m\"\u001b[39m] = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m   1725\u001b[39m \u001b[38;5;66;03m# now that things have been prepped, we do the systematic rewriting of `args`\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m1727\u001b[39m df_output, wide_id_vars = process_args_into_dataframe(\n\u001b[32m   1728\u001b[39m     args,\n\u001b[32m   1729\u001b[39m     wide_mode,\n\u001b[32m   1730\u001b[39m     var_name,\n\u001b[32m   1731\u001b[39m     value_name,\n\u001b[32m   1732\u001b[39m     is_pd_like,\n\u001b[32m   1733\u001b[39m     native_namespace,\n\u001b[32m   1734\u001b[39m )\n\u001b[32m   1735\u001b[39m df_output: nw.DataFrame\n\u001b[32m   1736\u001b[39m \u001b[38;5;66;03m# now that `df_output` exists and `args` contains only references, we complete\u001b[39;00m\n\u001b[32m   1737\u001b[39m \u001b[38;5;66;03m# the special-case and wide-mode handling by further rewriting args and/or mutating\u001b[39;00m\n\u001b[32m   1738\u001b[39m \u001b[38;5;66;03m# df_output\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/KAH/lib/python3.13/site-packages/plotly/express/_core.py:1328\u001b[39m, in \u001b[36mprocess_args_into_dataframe\u001b[39m\u001b[34m(args, wide_mode, var_name, value_name, is_pd_like, native_namespace)\u001b[39m\n\u001b[32m   1326\u001b[39m         \u001b[38;5;28;01mif\u001b[39;00m argument == \u001b[33m\"\u001b[39m\u001b[33mindex\u001b[39m\u001b[33m\"\u001b[39m:\n\u001b[32m   1327\u001b[39m             err_msg += \u001b[33m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33m To use the index, pass it in directly as `df.index`.\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m-> \u001b[39m\u001b[32m1328\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(err_msg)\n\u001b[32m   1329\u001b[39m \u001b[38;5;28;01<PERSON>if\u001b[39;00m length \u001b[38;5;129;01mand\u001b[39;00m (actual_len := \u001b[38;5;28mlen\u001b[39m(df_input)) != length:\n\u001b[32m   1330\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[32m   1331\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mAll arguments should have the same length. \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m   1332\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mThe length of column argument `df[\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m]` is \u001b[39m\u001b[38;5;132;01m%d\u001b[39;00m\u001b[33m, whereas the \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m   (...)\u001b[39m\u001b[32m   1339\u001b[39m         )\n\u001b[32m   1340\u001b[39m     )\n", "\u001b[31mValueError\u001b[39m: Value of 'x' is not the name of a column in 'data_frame'. Expected one of ['stocks'] but received: title"]}], "source": ["# 5. Visualisations avec Plotly\n", "# Graphique simple\n", "fig1 = px.bar(df_loaded, x='title', title='Stock Approvisionnement')\n", "fig1.show()\n", "\n", "# Graphique en secteurs\n", "fig2 = px.pie(df_loaded, names='title', title='Répartition des données')\n", "fig2.show()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Données prêtes pour l'analyse!\n", "DataFrame disponible: df_loaded\n", "Fichier CSV: ../../exports/gstockapprovisionnement_20250714_121000.csv\n"]}], "source": ["# 6. <PERSON><PERSON><PERSON><PERSON> pour les analyses\n", "print(\"\\nDonnées prêtes pour l'analyse!\")\n", "print(f\"DataFrame disponible: df_loaded\")\n", "print(f\"Fichier CSV: {csv_filepath}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python [conda env:KAH]", "language": "python", "name": "conda-env-KAH-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}