{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import os\n", "from datetime import datetime\n", "import plotly.express as px\n", "import plotly.graph_objects as go"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 1. Chargement des données JSON\n", "json_file = 'gstocksortie_data.json'\n", "\n", "with open(json_file, 'r', encoding='utf-8') as f:\n", "    json_data = json.load(f)\n", "\n", "print(f\"Données JSON chargées depuis {json_file}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2. Conversion JSON vers DataFrame\n", "if isinstance(json_data, list):\n", "    df = pd.DataFrame(json_data)\n", "else:\n", "    df = pd.DataFrame([json_data])\n", "\n", "print(f\"DataFrame créé: {df.shape}\")\n", "print(df.head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 3. <PERSON><PERSON><PERSON><PERSON> en CSV\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "csv_filename = f'gstocksortie_{timestamp}.csv'\n", "\n", "os.makedirs('../../exports', exist_ok=True)\n", "csv_filepath = f'../../exports/{csv_filename}'\n", "\n", "df.to_csv(csv_filepath, index=False, encoding='utf-8')\n", "print(f\"CSV sauvegardé: {csv_filepath}\")\n", "print(f\"Nombre de lignes: {len(df)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 4. Lecture du CSV sauvegardé\n", "df_loaded = pd.read_csv(csv_filepath)\n", "\n", "print(f\"CSV rechargé: {df_loaded.shape}\")\n", "print(\"Aperçu des données:\")\n", "print(df_loaded.head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 5. Visualisations avec Plotly\n", "# Graphique simple\n", "fig1 = px.bar(df_loaded, x='title', title='Stock Sortie')\n", "fig1.show()\n", "\n", "# Graphique en secteurs\n", "fig2 = px.pie(df_loaded, names='title', title='Répartition des données')\n", "fig2.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 6. <PERSON><PERSON><PERSON><PERSON> pour les analyses\n", "print(\"\\nDonnées prêtes pour l'analyse!\")\n", "print(f\"DataFrame disponible: df_loaded\")\n", "print(f\"Fichier CSV: {csv_filepath}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 4}