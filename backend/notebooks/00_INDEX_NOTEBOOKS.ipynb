{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# INDEX DES NOTEBOOKS - KAYDAN ANALYTICS HUB\n", "\n", "## LISTE DES NOTEBOOKS PAR CATÉ<PERSON><PERSON>IE\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### CHARGEMENT ET CONVERSION DE FICHIERS\n", "\n", "| Notebook | Description |\n", "|----------|-------------|\n", "| [userprofile_analysis.ipynb](userprofile_analysis.ipynb) | Chargement CSV et conversion JSON/XML vers CSV |\n", "| [ecoletalents_analysis.ipynb](ecoletalents_analysis.ipynb) | Chargement CSV et conversion JSON/XML vers CSV |\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### UTILISATEURS\n", "\n", "| Notebook | Modèle | Description |\n", "|----------|--------|-------------|\n", "| [userprofile_analysis.ipynb](userprofile_analysis.ipynb) | UserProfile | Profils utilisateur avec informations personnelles |\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### DQE (DEVIS QUANTITATIF ESTIMATIF)\n", "\n", "| Notebook | Modèle | Description |\n", "|----------|--------|-------------|\n", "| [dqedata_analysis.ipynb](dqedata_analysis.ipynb) | DQEData | Données DQE (Devis Quantitatif Estimatif) |\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### G-STOCK (GESTION DE STOCK)\n", "\n", "| Notebook | Modèle | Description |\n", "|----------|--------|-------------|\n", "| [gstockapprovisionnement_analysis.ipynb](gstockapprovisionnement_analysis.ipynb) | GStockApprovisionnement | Données d'approvisionnement de stock |\n", "| [gstocksortie_analysis.ipynb](gstocksortie_analysis.ipynb) | GStockSortie | Données de sortie de stock |\n", "| [gstockconsommation_analysis.ipynb](gstockconsommation_analysis.ipynb) | GStockConsommation | Données de consommation de stock |\n", "| [gstockachat_analysis.ipynb](gstockachat_analysis.ipynb) | GStockAchat | Données d'achat de matériaux |\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### PROJETS & FORMATION\n", "\n", "| Notebook | Modèle | Description |\n", "|----------|--------|-------------|\n", "| [gprojet_analysis.ipynb](gprojet_analysis.ipynb) | GProjet | Données des projets de construction |\n", "| [ecoletalents_analysis.ipynb](ecoletalents_analysis.ipynb) | EcoleTalents | Données de l'école des talents |\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### MACROÉCONOMIE\n", "\n", "| Notebook | Modèle | Description |\n", "|----------|--------|-------------|\n", "| [donneesproduitinterieurbrut_analysis.ipynb](donneesproduitinterieurbrut_analysis.ipynb) | DonneesProduitInterieurBrut | Données du Produit Intérieur Brut (PIB) |\n", "| [donneesinflation_analysis.ipynb](donneesinflation_analysis.ipynb) | donneesInflation | Données d'inflation par pays |\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### IMMOBILIER\n", "\n", "| Notebook | Modèle | Description |\n", "|----------|--------|-------------|\n", "| [donnestauxpretimmobilier_analysis.ipynb](donnestauxpretimmobilier_analysis.ipynb) | DonnesTauxPretImmobilier | Taux de prêt immobilier par banque |\n", "| [donneesprixmetrecarre_analysis.ipynb](donneesprixmetrecarre_analysis.ipynb) | DonneesPrixMetreCarre | Prix au mètre carré par commune |\n", "| [donneesmateriauxconstruction_analysis.ipynb](donneesmateriauxconstruction_analysis.ipynb) | DonneesMateriauxConstruction | Prix des matériaux de construction |\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### DÉMOGRAPHIE\n", "\n", "| Notebook | Modèle | Description |\n", "|----------|--------|-------------|\n", "| [donneesprojectiondemographique_analysis.ipynb](donneesprojectiondemographique_analysis.ipynb) | DonneesProjectionDemographique | Projections démographiques par région |\n", "| [donneesmigrationinterne_analysis.ipynb](donneesmigrationinterne_analysis.ipynb) | DonneesMigrationInterne | Données de migration interne entre régions |\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "## GUIDE D'UTILISATION\n", "\n", "### 1. <PERSON><PERSON><PERSON><PERSON>\n", "- Python 3.8+\n", "- Jupyter Notebook ou JupyterLab\n", "- Packages : pandas, mat<PERSON><PERSON><PERSON><PERSON>, seaborn, django\n", "\n", "### 2. Installation des dépendances\n", "```bash\n", "pip install jupyter pandas mat<PERSON><PERSON><PERSON>b seaborn django\n", "```\n", "\n", "### 3. <PERSON><PERSON>\n", "```bash\n", "cd notebooks/\n", "jupyter notebook\n", "```\n", "\n", "### 4. Utilisation des notebooks\n", "1. <PERSON><PERSON><PERSON> sur le notebook de votre choix\n", "2. <PERSON><PERSON><PERSON><PERSON> les cellules une par une (<PERSON><PERSON> + <PERSON><PERSON>)\n", "3. Les données seront automatiquement exportées en CSV\n", "4. Les visualisations s'afficheront directement\n", "\n", "---\n", "\n", "## STRUCTURE DES DOSSIERS\n", "\n", "```\n", "KAH/\n", "├── notebooks/              # Notebooks Jupyter\n", "│   ├── 00_INDEX_NOTEBOOKS.ipynb  # Ce fichier d'index\n", "│   ├── userprofile_analysis.ipynb\n", "│   ├── dqedata_analysis.ipynb\n", "│   └── ...\n", "├── exports/                # Fichiers CSV exportés\n", "├── backend/                # Code Django\n", "│   ├── api/models.py       # <PERSON>d<PERSON><PERSON> de données\n", "│   └── ...\n", "└── generate_notebooks.py   # Script de génération\n", "```\n", "\n", "---\n", "\n", "## RÉGÉNÉRATION DES NOTEBOOKS\n", "\n", "<PERSON>ur ré<PERSON><PERSON><PERSON><PERSON> tous les notebooks après modification des modèles :\n", "\n", "```bash\n", "python generate_notebooks.py\n", "```\n", "\n", "---\n", "\n", "## SUPPORT\n", "\n", "Pour toute question ou problème :\n", "- Email : <EMAIL>\n", "- Documentation : http://127.0.0.1:8000/doc/\n", "- Issues : GitHub repository\n", "\n", "*Index généré automatiquement - Kaydan Analytics Hub*"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Vérification rapide de l'environnement\n", "import sys\n", "import os\n", "from datetime import datetime\n", "\n", "print(\"VÉRIFICATION DE L'ENVIRONNEMENT\")\n", "print(\"=\" * 40)\n", "print(f\"Python version: {sys.version}\")\n", "print(f\"Répertoire courant: {os.getcwd()}\")\n", "print(f\"Date/Heure: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "\n", "# Vérification des packages\n", "packages = ['pandas', 'matplotlib', 'seaborn', 'django']\n", "print(\"\\nPackages installés:\")\n", "for package in packages:\n", "    try:\n", "        __import__(package)\n", "        print(f\"   OK {package}\")\n", "    except ImportError:\n", "        print(f\"   MANQUANT {package}\")\n", "\n", "# Vérification des dossiers\n", "folders = ['../backend', '../exports']\n", "print(\"\\nDossiers:\")\n", "for folder in folders:\n", "    if os.path.exists(folder):\n", "        print(f\"   OK {folder}\")\n", "    else:\n", "        print(f\"   MANQUANT {folder}\")\n", "\n", "print(\"\\nVérification terminée\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 4}