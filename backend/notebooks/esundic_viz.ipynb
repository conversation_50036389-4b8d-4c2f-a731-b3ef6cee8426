{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import os\n", "from datetime import datetime\n", "import plotly.express as px\n", "import plotly.graph_objects as go"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Données JSON chargées depuis E_syndic.json\n"]}], "source": ["# 1. Chargement des données JSON\n", "json_file = 'E_syndic.json'\n", "\n", "with open(json_file, 'r', encoding='utf-8') as f:\n", "    json_data = json.load(f)\n", "\n", "print(f\"Données JSON chargées depuis {json_file}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2. Conversion JSON vers DataFrame\n", "if isinstance(json_data, dict) and 'data' in json_data:\n", "    df = pd.DataFrame(json_data['data'])\n", "elif isinstance(json_data, list):\n", "    df = pd.DataFrame(json_data)\n", "else:\n", "    df = pd.DataFrame([json_data])\n", "\n", "print(f\"DataFrame créé: {df.shape}\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 3. <PERSON><PERSON><PERSON><PERSON> en CSV\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "csv_filename = f'esundic_{timestamp}.csv'\n", "\n", "os.makedirs('../exports', exist_ok=True)\n", "csv_filepath = f'../exports/{csv_filename}'\n", "\n", "df.to_csv(csv_filepath, index=False, encoding='utf-8')\n", "print(f\"CSV sauvegardé: {csv_filepath}\")\n", "print(f\"Nombre de lignes: {len(df)}\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'csv_filepath' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[3]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# 4. Lecture du CSV sauvegardé\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m df_loaded = pd.read_csv(csv_filepath)\n\u001b[32m      4\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mCSV rechargé: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mdf_loaded.shape\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m      5\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33mAperçu des données:\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[31mNameError\u001b[39m: name 'csv_filepath' is not defined"]}], "source": ["# 4. Lecture du CSV sauvegardé\n", "df_loaded = pd.read_csv(csv_filepath)\n", "\n", "print(f\"CSV rechargé: {df_loaded.shape}\")\n", "print(\"Aperçu des données:\")\n", "df_loaded.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 5. Extraction des données pour visualisation\n", "syndics = []\n", "for _, row in df_loaded.iterrows():\n", "    # Compter les propriétaires et locataires\n", "    proprietaires = eval(row['proprietaire']) if pd.notna(row['proprietaire']) and row['proprietaire'] != '[]' else []\n", "    locataires = eval(row['locataires']) if pd.notna(row['locataires']) and row['locataires'] != '[]' else []\n", "    incidents = eval(row['incidents']) if pd.notna(row['incidents']) and row['incidents'] != '[]' else []\n", "    evenements = eval(row['evenements']) if pd.notna(row['evenements']) and row['evenements'] != '[]' else []\n", "    \n", "    syndics.append({\n", "        'syndic': row['libelle'],\n", "        'ville': row['ville'],\n", "        'superficie': int(row['superficie']) if pd.notna(row['superficie']) else 0,\n", "        'nb_proprietaires': len(proprietaires),\n", "        'nb_locataires': len(locataires),\n", "        'nb_incidents': len(incidents),\n", "        'nb_evenements': len(evenements)\n", "    })\n", "\n", "df_viz = pd.DataFrame(syndics)\n", "print(df_viz)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 6. Visualisation 1: Répartition des syndics par ville\n", "fig1 = px.pie(df_viz, names='ville', title='Répartition des syndics par ville')\n", "fig1.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 7. Visualisation 2: Nombre de propriétaires par syndic\n", "fig2 = px.bar(df_viz, x='syndic', y='nb_proprietaires', \n", "              title='Nombre de propriétaires par syndic',\n", "              labels={'nb_proprietaires': 'Nombre de propriétaires', 'syndic': 'Syndic'})\n", "fig2.update_xaxis(tickangle=45)\n", "fig2.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 8. Visualisation 3: Superficie vs Nombre d'incidents\n", "fig3 = px.scatter(df_viz, x='superficie', y='nb_incidents', \n", "                  size='nb_proprietaires', hover_name='syndic',\n", "                  title='Superficie vs Nombre d\\'incidents (taille = nb propriétaires)',\n", "                  labels={'superficie': 'Superficie (m²)', 'nb_incidents': 'Nombre d\\'incidents'})\n", "fig3.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 9. Visualisation 4: Comparaison propriétaires vs locataires\n", "fig4 = go.Figure()\n", "fig4.add_trace(go.Bar(name='Pro<PERSON>riétaire<PERSON>', x=df_viz['syndic'], y=df_viz['nb_proprietaires']))\n", "fig4.add_trace(go.Bar(name='Locataires', x=df_viz['syndic'], y=df_viz['nb_locataires']))\n", "fig4.update_layout(barmode='group', title='Comparaison Propriétaires vs Locataires par syndic',\n", "                   xaxis_title='Syndic', yaxis_title='Nombre de personnes')\n", "fig4.update_xaxis(tickangle=45)\n", "fig4.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 10. Statistiques descriptives\n", "print(\"=== STATISTIQUES DESCRIPTIVES ===\")\n", "print(f\"Nombre total de syndics: {len(df_viz)}\")\n", "print(f\"Superficie totale: {df_viz['superficie'].sum():,} m²\")\n", "print(f\"Nombre total de propriétaires: {df_viz['nb_proprietaires'].sum()}\")\n", "print(f\"Nombre total de locataires: {df_viz['nb_locataires'].sum()}\")\n", "print(f\"Nombre total d'incidents: {df_viz['nb_incidents'].sum()}\")\n", "print(f\"Nombre total d'événements: {df_viz['nb_evenements'].sum()}\")\n", "print(\"\\n=== MOYENNES ===\")\n", "print(f\"Superficie moyenne: {df_viz['superficie'].mean():.2f} m²\")\n", "print(f\"Propriétaires par syndic: {df_viz['nb_proprietaires'].mean():.2f}\")\n", "print(f\"Locataires par syndic: {df_viz['nb_locataires'].mean():.2f}\")"]}], "metadata": {"kernelspec": {"display_name": "Python [conda env:KAH]", "language": "python", "name": "conda-env-KAH-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}