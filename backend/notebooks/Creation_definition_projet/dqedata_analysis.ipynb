import pandas as pd
import json
import os
from datetime import datetime
import plotly.express as px
import plotly.graph_objects as go

# 1. Chargement des données JSON
json_file = 'dqedata_data.json'

with open(json_file, 'r', encoding='utf-8') as f:
    json_data = json.load(f)

print(f"Données JSON chargées depuis {json_file}")

# 2. Conversion JSON vers DataFrame
if isinstance(json_data, list):
    df = pd.DataFrame(json_data)
else:
    df = pd.DataFrame([json_data])

print(f"DataFrame créé: {df.shape}")
df.head()

# 3. Sauvegarde en CSV
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
csv_filename = f'dqedata_{timestamp}.csv'

os.makedirs('../../exports', exist_ok=True)
csv_filepath = f'../../exports/{csv_filename}'

df.to_csv(csv_filepath, index=False, encoding='utf-8')
print(f"CSV sauvegardé: {csv_filepath}")
print(f"Nombre de lignes: {len(df)}")

# 4. Lecture du CSV sauvegardé
df_loaded = pd.read_csv(csv_filepath)

print(f"CSV rechargé: {df_loaded.shape}")
print("Aperçu des données:")
print(df_loaded.head())

# 5. Extraction des données pour visualisation
projets = []
for _, row in df_loaded.iterrows():
    data = eval(row['data'])
    projets.append({
        'projet': row['title'],
        'cout_total': data['total_cost'],
        'statut': data['status'],
        'nb_items': len(data['items'])
    })

df_viz = pd.DataFrame(projets)
print(df_viz)

# 6. Visualisations avec Plotly
# Graphique des coûts par projet
fig1 = px.bar(df_viz, x='projet', y='cout_total', 
              title='Coût total par projet DQE',
              color='cout_total')
fig1.show()

# Répartition par statut
fig2 = px.pie(df_viz, names='statut',
              title='Répartition des projets par statut')
fig2.show()

# 7. Graphique combiné
fig3 = go.Figure()
fig3.add_trace(go.Bar(name='Coût (millions)', x=df_viz['projet'], y=df_viz['cout_total']/1000000))
fig3.add_trace(go.Bar(name='Nb items', x=df_viz['projet'], y=df_viz['nb_items']))
fig3.update_layout(title='Coût vs Nombre d\'items par projet', barmode='group')
fig3.show()

# 8. Prêt pour les analyses
print("\nDonnées prêtes pour l'analyse!")
print(f"DataFrame disponible: df_loaded")
print(f"DataFrame visualisation: df_viz")
print(f"Fichier CSV: {csv_filepath}")